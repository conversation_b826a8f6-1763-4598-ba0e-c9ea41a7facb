<template>
  <div class="content" ref="fullscreenContent">


    <div class="unit" v-if="isFullscreen">流量单位：m³/s</div>
    <div class="fullscreen" @click="toggleFullScreen"></div>
    <div style="height: 100%; width: 100; background-color: transparent;" v-if="isFullscreen">
      <!-- 左第一列 -->
      <div class="item-number" :style="{ top: 'calc(411px - 16px)', left: '335px' }">{{ getData('合济七闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(470px - 16px)', left: '335px' }">{{ getData('合济六闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(532px - 16px)', left: '335px' }">{{ getData('合济五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(593px - 16px)', left: '335px' }">{{ getData('合济四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(654px - 16px)', left: '335px' }">{{ getData('合济三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(719px - 16px)', left: '335px' }">{{ getData('合济二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(790px - 16px)', left: '335px' }">{{ getData('合济一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(844px - 16px)', left: '335px' }">{{ getData('合济进水') }}</div>
      <!-- 左第二列 -->
      <div class="item-number" :style="{ top: '493px', left: '577px' }">{{ getData('永兰五闸') }}</div>
      <div class="item-number" :style="{ top: '555px', left: '577px' }">{{ getData('永兰四闸') }}</div>
      <div class="item-number" :style="{ top: '620px', left: '577px' }">{{ getData('永兰三闸') }}</div>
      <div class="item-number" :style="{ top: '715px', left: '577px' }">{{ getData('永兰二闸') }}</div>
      <!-- 左第三列 -->
      <div class="item-number" :style="{ top: 'calc(115px - 16px)', left: '685px' }">{{ getData('西乐泄水') }}</div>
      <div class="item-number" :style="{ top: 'calc(214px - 16px)', left: '645px' }">{{ getData('西乐四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(294px - 16px)', left: '645px' }">{{ getData('西乐三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(373px - 16px)', left: '645px' }">{{ getData('西乐二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(459px - 16px)', left: '645px' }">{{ getData('西乐一闸') }}</div>
      <!-- 左第四列 -->
      <div class="item-number" :style="{ top: 'calc(292px)', left: '778px' }">{{ getData('西乐南渠进水闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(458px)', left: '778px' }">{{ getData('西乐西渠进水闸') }}</div>
      <!-- 左第五列 -->
      <div class="item-number" :style="{ top: 'calc(147px - 16px)', left: '869px' }">{{ getData('西乐南渠尾口') }}</div>
      <div class="item-number" :style="{ top: 'calc(210px - 16px)', left: '869px' }">{{ getData('西乐南渠三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(251px - 16px)', left: '869px' }">{{ getData('西乐南渠二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(291px - 16px)', left: '869px' }">{{ getData('西乐南渠一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(335px - 16px)', left: '869px' }">{{ getData('西乐西渠三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(455px - 16px)', left: '869px' }">{{ getData('西乐西渠二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(401px - 16px)', left: '869px' }">{{ getData('西乐西渠一闸') }}</div>

      <!-- 左第六列 -->
      <div class="item-number" :style="{ top: 'calc(108px - 16px)', left: '980px' }">{{ getData('正稍八闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(173px - 16px)', left: '980px' }">{{ getData('正稍七闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(244px - 16px)', left: '980px' }">{{ getData('正稍六闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(307px - 16px)', left: '980px' }">{{ getData('正稍五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(369px - 16px)', left: '980px' }">{{ getData('正稍四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(437px - 16px)', left: '980px' }">{{ getData('正稍三闸') }}</div>

      <!-- 斜方向 -->
      <div class="item-number" :style="{ top: 'calc(460px)', left: '1064px' }">{{ getData('大退水进水闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(300px)', left: '1325px' }">{{ getData('大退水一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(228px)', left: '1393px' }">{{ getData('大退水二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(162px)', left: '1460px' }">{{ getData('大退水三闸') }}</div>

      <!-- 第一行 -->
      <div class="item-number" :style="{ top: 'calc(554px - 16px)', left: '994px' }">{{ getData('西乐进水') }}</div>
      <div class="item-number" :style="{ top: 'calc(554px - 16px)', left: '1058px' }">{{ getData('新华进水') }}</div>
      <!-- 第二行 -->
      <div class="item-number" :style="{ top: 'calc(610px)', left: '682px' }">{{ getData('西乐光明闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(610px)', left: '1024px' }">{{ getData('干渠二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(610px)', left: '1373px' }">{{ getData('新华一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(610px)', left: '1579px' }">{{ getData('新华二闸') }}</div>

      <!-- 第三行 -->
      <div class="item-number" :style="{ top: 'calc(696px)', left: '988px' }">{{ getData('永乐进水') }}</div>
      <div class="item-number" :style="{ top: 'calc(696px)', left: '1056px' }">{{ getData('永刚进水') }}</div>
      <div class="item-number" :style="{ top: 'calc(704px)', left: '1443px' }">{{ getData('永刚二闸') }}</div>

      <!-- 第4行 -->
      <div class="item-number" :style="{ top: 'calc(773px)', left: '700px' }">{{ getData('永兰一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(771px)', left: '1030px' }">{{ getData('干渠一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(771px)', left: '1235px' }">{{ getData('永刚一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(771px)', left: '1670px' }">{{ getData('永刚三闸') }}</div>

      <!-- 第5行 -->
      <div class="item-number" :style="{ top: 'calc(810px)', left: '1051px' }">{{ getData('北边一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(810px)', left: '1185px' }">{{ getData('北边二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(810px)', left: '1314px' }">{{ getData('北边三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(810px)', left: '1442px' }">{{ getData('北边四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(810px)', left: '1573px' }">{{ getData('北边五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(810px)', left: '1700px' }">{{ getData('北边六闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(827px)', left: '1837px' }">{{ getData('北边退水闸') }}</div>

      <!-- 第7行 -->
      <div class="item-number" :style="{ top: 'calc(987px)', left: '666px' }">{{ getData('南边一闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(987px)', left: '776px' }">{{ getData('南边二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(987px)', left: '890px' }">{{ getData('南边三闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(987px)', left: '1002px' }">{{ getData('南边四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(987px)', left: '1112px' }">{{ getData('南边五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(987px)', left: '1224px' }">{{ getData('南边六闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(987px)', left: '1332px' }">{{ getData('南边七闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(987px)', left: '1453px' }">{{ getData('南边八闸') }}</div>


      <div class="item-number" :style="{ top: 'calc(394px - 16px)', left: '1410px' }">{{ getData('新华西稍二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(478px - 16px)', left: '1410px' }">{{ getData('新华西稍一闸') }}</div>

      <div class="item-number" :style="{ top: 'calc(368px)', left: '1625px' }">{{ getData('新华四闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(465px)', left: '1610px' }">{{ getData('新华三闸') }}</div>


      <div class="item-number" :style="{ top: 'calc(623px)', left: '1493px' }">{{ getData('西济二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(682px)', left: '1493px' }">{{ getData('西济一闸') }}</div>

      <div class="item-number" :style="{ top: 'calc(583px)', left: '1672px' }">{{ getData('永刚五闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(700px)', left: '1630px' }">{{ getData('永刚四闸') }}</div>

      <div class="item-number" :style="{ top: 'calc(884px)', left: '698px' }">{{ getData('总干二闸') }}</div>
      <div class="item-number" :style="{ top: 'calc(835px)', left: '787px' }">{{ getData('干渠进水') }}</div>
      <div class="item-number" :style="{ top: 'calc(844px)', left: '879px' }">{{ getData('北边进水') }}</div>

    </div>


    <div style="width: 100%; height: 30px;position: absolute;bottom: 3px;" v-if="isFullscreen">
      <TimePlaySlider v-if="times.length" :times="times" @onTimeChange="onTimeChange" />
    </div>
  </div>
</template>

<script lang="jsx">
import TimePlaySlider from '@/components/TimePlaySlider/index.vue'
import screenfull from 'screenfull'
import { getOptions } from '@/api/common'
import { mapboxPopup } from './popup.js'
import initLayer from './initLayer.js'

export default {
  name: 'GeneralizedMap',
  props: ['mapData'],
  components: { TimePlaySlider },
  data() {
    return {
      mapIns: null,
      times: [],
      isFullscreen: false,
      currentTime: '',
    }
  },
  computed: {},
  watch: {
    mapData: {
      handler(newVal, oldVal) {},
      deep: true,
    },
  },
  mounted() {
    let firstkey = Object.keys(this.mapData).length ? Object.keys(this.mapData)[0] : ''
    console.log("this.firstkey = ",firstkey)
    if(firstkey){
      this.times = [...new Set(Object.keys(this.mapData[firstkey]?.timeData))] 
      console.log("this.times = ",this.times)
      this.currentTime = this.times[0] || ''
    }
  },
  methods: {
    getData(name) {
      if (this.mapData[name]) {
        return this.mapData[name].timeData[this.currentTime].q
      } else {
        return '暂无'
      }
    },
    toggleFullScreen() {
      if (screenfull.isEnabled) {
        this.isFullscreen = !this.isFullscreen
        screenfull.toggle(this.$refs.fullscreenContent)
      } else {
        this.$message({
          message: '您的浏览器不支持全屏功能',
          type: 'warning'
        })
      }
    },
    onTimeChange(time) {
      this.currentTime = time
    }
  },
}
</script>

<style lang="less" scoped>
.content {
  height: 100%;
  width: 100%;
  margin: 5px 10px;
  // background: #f5f5f5;
  background: url('~@/assets/images/GeneralizedMap.png') no-repeat;
  background-size: 100% 100%;
  // display: flex;
  position: relative;

  .fullscreen {
    position: absolute;
    top: 10px;
    right: 10px;
    background: url('~@/assets/images/fullscreen.png') no-repeat;
    height: 40px;
    width: 40px;
  }

  .unit {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    font-size: 12px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0em;
    color: #1d2129;
    align-items: center;
  }

  .item-number {
    position: absolute;
    height: 14px;
    border-radius: 2px;
    opacity: 1;
    font-family: PingFang SC;
    font-size: 10px;
    font-weight: 500;
    line-height: 14px;
    display: flex;
    align-items: center;
    letter-spacing: -0.09px;
    z-index: 0;
    background: #FFF7E8;
    box-sizing: border-box;
    border: 1px solid #FFE4BA;
    display: flex;
    flex-direction: column;
    padding: 0px 2px;
    gap: 10px;
    color: #FF7D00;
  }

}
</style>
